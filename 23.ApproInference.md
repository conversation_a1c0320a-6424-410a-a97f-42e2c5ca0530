# 近似推断

这一讲中的近似推断具体描述在深度生成模型中的近似推断。推断的目的有下面几个部分：

1.  推断本身，根据结果（观测）得到原因（隐变量）。
2.  为参数的学习提供帮助。

但是推断本身是一个困难的额任务，计算复杂度往往很高，对于无向图，由于节点之间的联系过多，那么因子分解很难进行，并且相互之间都有耦合，于是很难求解，仅仅在某些情况如 RBM 中可解，在有向图中，常常由于条件独立性问题，如两个节点之间条件相关（explain away），于是求解这些节点的条件概率就很困难，仅仅在某些概率假设情况下可解如高斯模型，于是需要近似推断。

事实上，我们常常讲推断问题变为优化问题，即：
$$
Log-likehood:\sum\limits_{v\in V}\log p(v)
$$
对上面这个问题，由于：
$$
\log p(v)=\log\frac{p(v,h)}{p(h|v)}=\log\frac{p(v,h)}{q(h|v)}+\log\frac{q(h|v)}{p(h|v)}
$$
左右两边对 $h$ 积分：
$$
\int_h\log p(v)\cdot q(h|v)dh=\log p(v)
$$
右边积分有：
$$
\mathbb{E}_{q(h|v)}[\log\frac{p(v,h)}{q(h|v)}]+KL(q(h|v)||p(h|v))=\mathbb{E}_{q(h|v)}[\log p(v,h)]+H(q)+KL(q||p)
$$
其中前两项是 ELBO，于是这就变成一个优化 ELBO 的问题。